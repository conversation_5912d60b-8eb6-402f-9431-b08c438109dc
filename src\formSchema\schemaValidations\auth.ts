import * as Yup from 'yup'

const today = new Date();
const eighteenYearsAgo = new Date();
eighteenYearsAgo.setFullYear(today.getFullYear() - 18);

export const SignupSchemaValidation = Yup.object().shape({
    seekingFor: Yup.number().required("Please select your preference"),
    gender: Yup.number().required("Please select your gender"),
    username: Yup.string().min(3, "Too short").required("Username is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    dob: Yup.date()
        .max(eighteenYearsAgo, 'You must be at least 18 years old')
        .required('Date of birth is required'),
    countryId: Yup.number().required("Country is required"),
    // city: Yup.string().required("City is required"),
    password: Yup.string()
        .min(6, "Password must be at least 6 characters")
        .required("Password is required"),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref("password")], "Passwords must match")
        .required("Confirm password is required"),
    agreeTerms: Yup.boolean().oneOf([true], "You must accept the terms"),
});
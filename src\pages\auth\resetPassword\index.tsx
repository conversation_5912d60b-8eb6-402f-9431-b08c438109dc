import { useResetPasswordMutation } from "@/api";
import Password<PERSON>ield from "@/components/common/PasswordField";
import {
  confirmPasswordValidation,
  passwordValidation,
} from "@/formSchema/schemaValidations";
import { ROUTE_PATH } from "@/routes";
import { useFormik } from "formik";
import React from "react";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as Yup from "yup";
import { useTranslation } from '@/hooks/useTranslation';
import parse from 'html-react-parser';

const ResetPassword: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const email = searchParams.get("email") || undefined;

  const { mutateAsync: resetPassword, isPending } = useResetPasswordMutation();

  const formik = useFormik({
    initialValues: {
      password: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      password: passwordValidation("Password"),
      confirmPassword: confirmPasswordValidation("password"),
    }),
    onSubmit: async (values: any, { setSubmitting }: any) => {
      if (!email) return;
      try {
        const payload = {
          password: values.password,
          email,
        };
        const response: any = await resetPassword(payload);
        if (response?.success) {
          toast.success(response?.message);
          navigate(ROUTE_PATH.LOGIN);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setSubmitting(false);
      }
    },
  });

  const { values, handleChange, handleBlur, touched, errors } = formik;

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4 gap-sm-5">
        <div className="auth-form-heading text-center">
          <h3 className="mb-2 mb-sm-3">{parse(t('resetPassword.heading'))}</h3>
          <p className="mb-0">{t('resetPassword.subheading')}</p>
        </div>
        <Form onSubmit={formik.handleSubmit}>
          <div className="form-input-group d-flex flex-column">
            <PasswordField
              label={t('resetPassword.password')}
              name="password"
              placeholder={t('resetPassword.passwordPlaceholder')}
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
            />

            <PasswordField
              label={t('resetPassword.confirmPassword')}
              name="confirmPassword"
              placeholder={t('resetPassword.confirmPasswordPlaceholder')}
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
            />
          </div>
          <Button type="submit" className="w-100 mt-3 mt-sm-4">
            {isPending ? t('resetPassword.pleaseWait') : t('resetPassword.submit')}
          </Button>
        </Form>
      </div>
    </div>
  );
};

export default ResetPassword;

import { useRequestOTPMutation } from "@/api";
import { emailValidation } from "@/formSchema/schemaValidations";
import { ROUTE_PATH } from "@/routes";
import { ForgotInterface } from "@/types";
import { useFormik } from "formik";
import React from "react";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import { useTranslation } from '@/hooks/useTranslation';
import parse from 'html-react-parser';

const ForgotPassword: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync: requestOtp } = useRequestOTPMutation();

  const formik = useFormik({
    initialValues: {
      email: "",
      type: "FORGOT_PASSWORD",
    },
    validationSchema: Yup.object({
      email: emailValidation,
    }),
    onSubmit: async (values: ForgotInterface, { setSubmitting }: any) => {
      try {
        const result: any = await requestOtp(values);
        if (result?.success) {
          toast.success(result?.message);
          navigate(
            `${ROUTE_PATH.VERIFYACCOUNT}?type=${values.type}&email=${values.email}`
          );
        }
      } catch (error: any) {
        console.log(error?.response?.data?.message);
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4 gap-sm-5">
        <div className="auth-form-heading text-center">
          <h3 className="mb-2 mb-sm-3">{parse(t('forgotPassword.heading'))}</h3>
          <p className="mb-0">{t('forgotPassword.subheading')}</p>
        </div>

        <Form onSubmit={formik.handleSubmit} noValidate>
          <div className="form-input-group d-flex flex-column">
            <Form.Group className="form-input" controlId="email">
            <Form.Label>{t('forgotPassword.email')}</Form.Label>
              <Form.Control
                type="email"
                name="email"
                placeholder={t('forgotPassword.emailPlaceholder')}
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isInvalid={formik.touched.email && !!formik.errors.email}
              />
              <Form.Control.Feedback type="invalid">
                {formik.errors.email}
              </Form.Control.Feedback>
            </Form.Group>
          </div>

          <Button
            type="submit"
            className="w-100 mt-3 mt-sm-4"
            disabled={formik.isSubmitting}
          >
            {t('forgotPassword.submit')}
          </Button>
        </Form>
      </div>
    </div>
  );
};

export default ForgotPassword;

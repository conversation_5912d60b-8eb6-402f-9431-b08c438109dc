import {
  useRequestOTPMutation,
  useSignupMutation,
  useVerifyOTPMutation,
} from "@/api/auth.api";
import { useTranslation } from "@/hooks/useTranslation";
import { ROUTE_PATH } from "@/routes";
import parse from 'html-react-parser';
import { setUserInfo } from "@/stores";
import useAuthStore from "@/stores/auth";
import { useFormik } from "formik";
import React, { useEffect } from "react";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import OTPInput from "react-otp-input";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as Yup from "yup";
import "./styles.scss";

const RESEND_OTP_TIMER_SECONDS = 30;

const VerifyAccount: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync: verifyOtp, isPending: isVerifying } =
    useVerifyOTPMutation();
  const { mutateAsync: signup, isPending: isSigningUp } = useSignupMutation();
  const { mutateAsync: resendOtp, isPending: isResending } =
    useRequestOTPMutation();
  const signupUserInfo = useAuthStore((state) => state.signupUserInfo);
  const clearSignupUserInfo = useAuthStore(
    (state) => state.clearSignupUserInfo
  );

  const [timer, setTimer] = React.useState<number>(0);
  const timerRef = React.useRef<NodeJS.Timeout | null>(null);

  const [searchParams] = useSearchParams();
  const email = searchParams.get("email") || undefined;
  const type = searchParams.get("type") || undefined;

  const startTimer = () => {
    setTimer(RESEND_OTP_TIMER_SECONDS);
    timerRef.current = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          if (timerRef.current) clearInterval(timerRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const formik = useFormik({
    initialValues: { otp: "" },
    validationSchema: Yup.object({
      otp: Yup.string()
        .length(4, t("validation.otpLength"))
        .required(t("validation.otpRequired")),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const result: any = await verifyOtp({
          type,
          email,
          otp: values.otp,
        });
        if (result?.success) {
          if (type === "SIGN_UP" && signupUserInfo) {
            const response: any = await signup(signupUserInfo);
            if (response?.success) {
              setUserInfo(response?.data);
              clearSignupUserInfo();
              navigate(ROUTE_PATH.PROFILE_MANAGEMENT);
            }
          } else if (type === "FORGOT_PASSWORD") {
            toast.success(result?.message);
            navigate(`${ROUTE_PATH.RESETPASSWORD}?email=${email}`);
          }
        }
      } catch (err) {
        console.error("OTP verification or signup failed", err);
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleResend = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (timer > 0) return;

    try {
      const result: any = await resendOtp({
        type,
        email,
        username: signupUserInfo?.username,
      });
      if (result?.success) {
        toast.success(result?.message);
        startTimer();
      }
    } catch (err) {
      console.error("Resend OTP failed", err);
    }
  };

  useEffect(() => {
    if (formik.values.otp?.length === 4) {
      formik.submitForm();
    }

    const keyDownHandler = (event: any) => {
      if (event?.key === "Enter") {
        formik.submitForm();
      }
    };

    document.addEventListener("keydown", keyDownHandler);

    return () => {
      document.removeEventListener("keydown", keyDownHandler);
    };
  }, [formik.values.otp]);

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4 gap-sm-5">
        <div className="auth-form-heading text-center">
          <h3 className="mb-2 mb-sm-3">{parse(t("verifyAccount.heading"))}</h3>
          <p className="mb-0">{t("verifyAccount.subheading")}</p>
        </div>
        <Form noValidate onSubmit={formik.handleSubmit}>
          <div className="form-input-group d-flex flex-column">
            <div className="otp-box mb-2 mb-sm-4">
              <OTPInput
                value={formik.values.otp}
                onChange={(val) => {
                  formik.setFieldValue("otp", val);
                  if (val.length === 4) {
                    formik.submitForm();
                  }
                }}
                numInputs={4}
                renderSeparator={<span>-</span>}
                inputType="number"
                renderInput={(props) => (
                  <input {...props} type="tel" inputMode="numeric" />
                )}
                shouldAutoFocus
              />
              {formik.touched.otp && formik.errors.otp && (
                <div className="text-danger mt-2">{formik.errors.otp}</div>
              )}
            </div>
          </div>
          <Button
            type="submit"
            className="w-100 mt-3 mt-sm-4"
            disabled={formik.isSubmitting || isVerifying || isSigningUp}
          >
            {isVerifying || isSigningUp
              ? t("verifyAccount.verifying")
              : t("verifyAccount.submit")}
          </Button>
        </Form>
      </div>
      <div className="text-center mt-3 mt-sm-4">
        <p className="mb-0">
          {t("verifyAccount.noOtp")}{" "}
          <button
            onClick={handleResend}
            style={{
              pointerEvents: timer > 0 ? "none" : "auto",
              opacity: timer > 0 ? 0.6 : 1,
              background: "transparent",
            }}
            className="border-0 color-primary fw-bold p-0"
          >
            {isResending
              ? t("verifyAccount.resending")
              : timer > 0
                ? t("verifyAccount.resendIn", { seconds: timer })
                : t("verifyAccount.resend")}
          </button>
        </p>
      </div>
    </div>
  );
};

export default VerifyAccount;
